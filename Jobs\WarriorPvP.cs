using System;
using System.Linq;
using ff14bot;
using ff14bot.Enums;
using ff14bot.Managers;
using ff14bot.Objects;
using TreeSharp;

namespace PvPRotation.Jobs
{
    public static class WarriorPvP
    {
        private static LocalPlayer Me => Core.Me;
        private static GameObject Target => Core.Me.CurrentTarget;

        // Warrior PvP Action IDs (actual PvP ability IDs)
        private const uint HeavySwing = 29074;
        private const uint Maim = 29075;
        private const uint StormsPath = 29076;
        private const uint StormsEye = 29077;
        private const uint Tomahawk = 29078;
        private const uint Overpower = 29079;
        private const uint Berserk = 29080;
        private const uint ThrillOfBattle = 29081;
        private const uint Holmgang = 29082;
        private const uint Vengeance = 29083;
        private const uint InnerBeast = 29084;
        private const uint SteelCyclone = 29085;
        private const uint Infuriate = 29086;
        private const uint FellCleave = 29087;
        private const uint Decimate = 29088;
        private const uint Onslaught = 29089;
        private const uint Upheaval = 29090;
        private const uint InnerRelease = 29091;

        // PvP specific abilities
        private const uint BloodForBlood = 29092;
        private const uint Bloodbath = 29093;
        private const uint PrimalRend = 29094;

        public static Composite CreateWarriorPvPRotation()
        {
            return new PrioritySelector(
                // Emergency abilities
                CreateEmergencyActions(),

                // Buff maintenance
                CreateBuffActions(),

                // Offensive rotation
                CreateOffensiveRotation(),

                // Gap closers and utility
                CreateUtilityActions()
            );
        }

        private static Composite CreateEmergencyActions()
        {
            return new PrioritySelector(
                // Holmgang when very low health
                new Decorator(ret => Me.CurrentHealthPercent < 15 && CanUseAbility(Holmgang),
                    new Action(ret => UseAbility(Holmgang, Me))),

                // Vengeance for damage reduction
                new Decorator(ret => Me.CurrentHealthPercent < 40 && CanUseAbility(Vengeance) && EnemiesNearby() >= 2,
                    new Action(ret => UseAbility(Vengeance, Me))),

                // Thrill of Battle for emergency healing
                new Decorator(ret => Me.CurrentHealthPercent < 50 && CanUseAbility(ThrillOfBattle),
                    new Action(ret => UseAbility(ThrillOfBattle, Me)))
            );
        }

        private static Composite CreateBuffActions()
        {
            return new PrioritySelector(
                // Primal Rend - PvP specific finisher
                new Decorator(ret => CanUseAbility(PrimalRend) && Me.HasAura("Primal Rend Ready") &&
                                   HasValidTarget(),
                    new Action(ret => UseAbility(PrimalRend, Target))),

                // Blood for Blood - PvP damage buff
                new Decorator(ret => CanUseAbility(BloodForBlood) && HasValidTarget() &&
                                   Target.CurrentHealthPercent > 50,
                    new Action(ret => UseAbility(BloodForBlood, Me))),

                // Bloodbath - PvP healing
                new Decorator(ret => CanUseAbility(Bloodbath) && Me.CurrentHealthPercent < 60,
                    new Action(ret => UseAbility(Bloodbath, Me))),

                // Inner Release for burst damage
                new Decorator(ret => CanUseAbility(InnerRelease) && HasValidTarget() &&
                                   Target.CurrentHealthPercent > 30,
                    new Action(ret => UseAbility(InnerRelease, Me))),

                // Berserk for damage boost
                new Decorator(ret => CanUseAbility(Berserk) && HasValidTarget() &&
                                   !Me.HasAura("Inner Release"),
                    new Action(ret => UseAbility(Berserk, Me))),

                // Infuriate for Beast Gauge
                new Decorator(ret => CanUseAbility(Infuriate) && Me.ClassLevel >= 50 &&
                                   GetBeastGauge() < 50,
                    new Action(ret => UseAbility(Infuriate, Me)))
            );
        }

        private static Composite CreateOffensiveRotation()
        {
            return new PrioritySelector(
                // AoE rotation when multiple enemies
                new Decorator(ret => PvPSettings.Instance.UseAoE && EnemiesNearby(5f) >= 3,
                    CreateAoERotation()),

                // Single target rotation
                CreateSingleTargetRotation()
            );
        }

        private static Composite CreateAoERotation()
        {
            return new PrioritySelector(
                // Decimate with Inner Release
                new Decorator(ret => CanUseAbility(Decimate) && Me.HasAura("Inner Release"),
                    new Action(ret => UseAbility(Decimate, Target))),

                // Steel Cyclone with Beast Gauge
                new Decorator(ret => CanUseAbility(SteelCyclone) && GetBeastGauge() >= 50,
                    new Action(ret => UseAbility(SteelCyclone, Target))),

                // Overpower for AoE combo
                new Decorator(ret => CanUseAbility(Overpower),
                    new Action(ret => UseAbility(Overpower, Target)))
            );
        }

        private static Composite CreateSingleTargetRotation()
        {
            return new PrioritySelector(
                // Fell Cleave with Inner Release
                new Decorator(ret => CanUseAbility(FellCleave) && Me.HasAura("Inner Release"),
                    new Action(ret => UseAbility(FellCleave, Target))),

                // Inner Beast with Beast Gauge
                new Decorator(ret => CanUseAbility(InnerBeast) && GetBeastGauge() >= 50,
                    new Action(ret => UseAbility(InnerBeast, Target))),

                // Upheaval for oGCD damage
                new Decorator(ret => CanUseAbility(Upheaval) && HasValidTarget(),
                    new Action(ret => UseAbility(Upheaval, Target))),

                // Storm's Eye for damage buff (if not up)
                new Decorator(ret => CanUseAbility(StormsEye) && !Target.HasMyAura("Storm's Eye") &&
                                   Me.HasAura("Maim"),
                    new Action(ret => UseAbility(StormsEye, Target))),

                // Storm's Path for healing
                new Decorator(ret => CanUseAbility(StormsPath) && Me.CurrentHealthPercent < 70 &&
                                   Me.HasAura("Maim"),
                    new Action(ret => UseAbility(StormsPath, Target))),

                // Maim combo
                new Decorator(ret => CanUseAbility(Maim) && Me.HasAura("Heavy Swing"),
                    new Action(ret => UseAbility(Maim, Target))),

                // Heavy Swing starter
                new Decorator(ret => CanUseAbility(HeavySwing),
                    new Action(ret => UseAbility(HeavySwing, Target)))
            );
        }

        private static Composite CreateUtilityActions()
        {
            return new PrioritySelector(
                // Onslaught for gap closing
                new Decorator(ret => CanUseAbility(Onslaught) && HasValidTarget() &&
                                   Target.Distance() > 10f && Target.Distance() < 20f,
                    new Action(ret => UseAbility(Onslaught, Target))),

                // Tomahawk for ranged pulling
                new Decorator(ret => CanUseAbility(Tomahawk) && HasValidTarget() &&
                                   Target.Distance() > 15f && Target.Distance() < 25f,
                    new Action(ret => UseAbility(Tomahawk, Target)))
            );
        }

        #region Helper Methods

        private static bool HasValidTarget()
        {
            return Target != null && Target.IsValid && Target.IsAlive &&
                   Target.CanAttack && Target.Distance() <= 30f;
        }

        private static bool CanUseAbility(uint abilityId)
        {
            return ActionManager.HasSpell(abilityId) &&
                   ActionManager.CanCast(abilityId, Target);
        }

        private static bool UseAbility(uint abilityId, GameObject target)
        {
            if (PvPSettings.Instance.DebugLogging)
            {
                PvPSettings.Instance.LogDebug($"Using ability {abilityId} on {target?.Name ?? "self"}");
            }

            return ActionManager.DoAction(abilityId, target);
        }

        private static int EnemiesNearby(float range = 10f)
        {
            return GameObjectManager.GetObjectsOfType<BattleCharacter>()
                .Count(x => x.IsValid && x.IsAlive && x.CanAttack &&
                           x.Distance() <= range);
        }

        private static int GetBeastGauge()
        {
            // This would need to be implemented based on the actual gauge system
            // For now, return a placeholder value
            return 0;
        }

        #endregion
    }
}
