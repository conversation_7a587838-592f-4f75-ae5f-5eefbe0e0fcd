using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Media;
using Buddy.Coroutines;
using ff14bot;
using ff14bot.Behavior;
using ff14bot.Enums;
using ff14bot.Helpers;
using ff14bot.Managers;
using ff14bot.Navigation;
using ff14bot.Objects;
using ff14bot.RemoteWindows;
using TreeSharp;
using PvPRotation.Jobs;

namespace PvPRotation
{
    public class PvPRotation : CombatRoutine
    {
        public override string Name => "PvP Rotation";
        public override string Author => "YourName";
        public override string Description => "A customizable PvP rotation for Final Fantasy XIV";
        public override string Version => "1.0.0";

        public override bool WantButton => true;

        private static readonly object LockObject = new object();

        // PvP specific properties
        private static bool InPvP => PvpZone.InPvP;
        private static LocalPlayer Me => Core.Me;
        private static GameObject Target => Core.Me.CurrentTarget;

        public override void OnButtonPress()
        {
            // Open settings window when button is pressed
            PvPSettings.Instance.IsOpen = true;
        }

        public override void Pulse()
        {
            // Main pulse method - called continuously
            if (!InPvP) return;

            // Update any necessary data here
            UpdateTargeting();
        }

        public override void ShutDown()
        {
            // Cleanup when routine is shut down
            Logging.Write(Colors.Red, "[PvP Rotation] Shutting down...");
        }

        public override void Initialize()
        {
            // Initialize the rotation
            Logging.Write(Colors.Green, "[PvP Rotation] Initialized successfully!");
        }

        protected override Composite CreateBehavior()
        {
            return new PrioritySelector(
                // Emergency actions first
                CreateEmergencyBehavior(),

                // Offensive actions
                CreateOffensiveBehavior(),

                // Movement and positioning
                CreateMovementBehavior(),

                // Default behavior
                CreateDefaultBehavior()
            );
        }

        #region Behavior Trees

        private Composite CreateEmergencyBehavior()
        {
            return new PrioritySelector(
                // Purify (dispel debuffs)
                new Decorator(ret => ShouldUsePurify(),
                    new Action(ret => UsePurify())),

                // Recuperate (heal)
                new Decorator(ret => ShouldUseRecuperate(),
                    new Action(ret => UseRecuperate())),

                // Guard (damage reduction)
                new Decorator(ret => ShouldUseGuard(),
                    new Action(ret => UseGuard()))
            );
        }

        private Composite CreateOffensiveBehavior()
        {
            return new PrioritySelector(
                // Target validation
                new Decorator(ret => !HasValidTarget(),
                    new Action(ret => SelectBestTarget())),

                // Job-specific rotation logic will go here
                CreateJobSpecificRotation(),

                // Basic attack if nothing else
                new Decorator(ret => CanUseBasicAttack(),
                    new Action(ret => UseBasicAttack()))
            );
        }

        private Composite CreateMovementBehavior()
        {
            return new PrioritySelector(
                // Sprint if needed
                new Decorator(ret => ShouldSprint(),
                    new Action(ret => UseSprint())),

                // Move to optimal position
                new Decorator(ret => ShouldRepositionForCombat(),
                    new Action(ret => MoveToOptimalPosition()))
            );
        }

        private Composite CreateDefaultBehavior()
        {
            return new Action(ret => RunStatus.Success);
        }

        private Composite CreateJobSpecificRotation()
        {
            // Determine job and return appropriate rotation
            switch (Me.CurrentJob)
            {
                case ClassJobType.Warrior:
                    return WarriorPvP.CreateWarriorPvPRotation();

                case ClassJobType.Paladin:
                    // TODO: Implement Paladin rotation
                    break;

                case ClassJobType.DarkKnight:
                    // TODO: Implement Dark Knight rotation
                    break;

                // Add more jobs as needed
                default:
                    PvPSettings.Instance.LogDebug($"No specific rotation found for {Me.CurrentJob}");
                    break;
            }

            // Fallback rotation for unsupported jobs
            return new PrioritySelector(
                // Limit Break
                new Decorator(ret => ShouldUseLimitBreak(),
                    new Action(ret => UseLimitBreak())),

                // Basic attack pattern
                new Action(ret => RunStatus.Failure)
            );
        }

        #endregion

        #region Utility Methods

        private void UpdateTargeting()
        {
            // Update target selection logic
            if (!HasValidTarget())
            {
                SelectBestTarget();
            }
        }

        private bool HasValidTarget()
        {
            return Target != null &&
                   Target.IsValid &&
                   Target.IsAlive &&
                   Target.CanAttack &&
                   Target.Distance() <= 30f;
        }

        private void SelectBestTarget()
        {
            var enemies = GameObjectManager.GetObjectsOfType<BattleCharacter>()
                .Where(x => x.IsValid &&
                           x.IsAlive &&
                           x.CanAttack &&
                           x.IsTargetable &&
                           x.Distance() <= 30f)
                .OrderBy(x => x.Distance())
                .ToList();

            var bestTarget = enemies.FirstOrDefault();
            if (bestTarget != null)
            {
                bestTarget.Target();
            }
        }

        #endregion

        #region PvP Abilities

        private bool ShouldUsePurify()
        {
            return Me.HasAura("Heavy") ||
                   Me.HasAura("Bind") ||
                   Me.HasAura("Sleep") ||
                   Me.CurrentHealthPercent < PvPSettings.Instance.PurifyHealthThreshold;
        }

        private bool UsePurify()
        {
            return ActionManager.DoAction("Purify", Me);
        }

        private bool ShouldUseRecuperate()
        {
            return Me.CurrentHealthPercent < PvPSettings.Instance.RecuperateHealthThreshold &&
                   !Me.HasAura("Recuperate");
        }

        private bool UseRecuperate()
        {
            return ActionManager.DoAction("Recuperate", Me);
        }

        private bool ShouldUseGuard()
        {
            return Me.CurrentHealthPercent < PvPSettings.Instance.GuardHealthThreshold &&
                   EnemiesNearby() >= 2;
        }

        private bool UseGuard()
        {
            return ActionManager.DoAction("Guard", Me);
        }

        private bool ShouldSprint()
        {
            return !Me.HasAura("Sprint") &&
                   (Target?.Distance() > 15f || ShouldRetreat());
        }

        private bool UseSprint()
        {
            return ActionManager.DoAction("Sprint", Me);
        }

        private bool ShouldUseLimitBreak()
        {
            return PvPSettings.Instance.UseLimitBreak &&
                   EnemiesNearby() >= PvPSettings.Instance.LimitBreakEnemyCount;
        }

        private bool UseLimitBreak()
        {
            // This will depend on the job's limit break
            return false; // Placeholder
        }

        private bool CanUseBasicAttack()
        {
            return HasValidTarget() && Target.Distance() <= 3f;
        }

        private bool UseBasicAttack()
        {
            return ActionManager.DoAction("Attack", Target);
        }

        #endregion

        #region Helper Methods

        private int EnemiesNearby(float range = 10f)
        {
            return GameObjectManager.GetObjectsOfType<BattleCharacter>()
                .Count(x => x.IsValid &&
                           x.IsAlive &&
                           x.CanAttack &&
                           x.Distance() <= range);
        }

        private bool ShouldRetreat()
        {
            return Me.CurrentHealthPercent < 30f && EnemiesNearby() >= 3;
        }

        private bool ShouldRepositionForCombat()
        {
            if (!HasValidTarget()) return false;

            var distance = Target.Distance();
            return distance > 25f || distance < 3f;
        }

        private void MoveToOptimalPosition()
        {
            if (!HasValidTarget()) return;

            var distance = Target.Distance();
            if (distance > 20f)
            {
                // Move closer
                Navigator.MoveTo(Target.Location);
            }
            else if (distance < 5f && ShouldRetreat())
            {
                // Move away
                var retreatPosition = Me.Location + (Me.Location - Target.Location).Normalized() * 10f;
                Navigator.MoveTo(retreatPosition);
            }
        }

        #endregion
    }
}
