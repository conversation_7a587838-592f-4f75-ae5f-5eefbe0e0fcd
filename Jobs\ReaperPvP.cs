using System;
using System.Linq;
using ff14bot;
using ff14bot.Enums;
using ff14bot.Managers;
using ff14bot.Objects;
using TreeSharp;

namespace PvPRotation.Jobs
{
    public static class ReaperPvP
    {
        private static LocalPlayer Me => Core.Me;
        private static GameObject Target => Core.Me.CurrentTarget;

        // Reaper PvP Action IDs (these are the actual PvP ability IDs)
        private const uint Slice = 29538;
        private const uint WaxingSlice = 29539;
        private const uint InfernalSlice = 29540;
        private const uint ShadowOfDeath = 29541;
        private const uint SoulSlice = 29542;
        private const uint Gibbet = 29543;
        private const uint Gallows = 29544;
        private const uint Guillotine = 29545;
        private const uint BloodStalk = 29546;
        private const uint GrimSwathe = 29547;
        private const uint Gluttony = 29548;
        private const uint Enshroud = 29549;
        private const uint Communio = 29550;
        private const uint LemuresSlice = 29551;
        private const uint LemuresScythe = 29552;
        private const uint VoidReaping = 29553;
        private const uint CrossReaping = 29554;
        private const uint GrimReaping = 29555;
        private const uint HarvestMoon = 29556;
        private const uint PlentifulHarvest = 29557;
        private const uint ArcaneCircle = 29558;
        private const uint HellsIngress = 29559;
        private const uint HellsEgress = 29560;
        private const uint Regress = 29561;

        // PvP specific abilities
        private const uint DeathWarrant = 29562;
        private const uint TenebraeLemurum = 29563;
        private const uint Perfectio = 29564;

        public static Composite CreateReaperPvPRotation()
        {
            return new PrioritySelector(
                // Emergency abilities
                CreateEmergencyActions(),

                // Shroud management and burst
                CreateShroudActions(),

                // Soul Gauge abilities
                CreateSoulGaugeActions(),

                // Offensive rotation
                CreateOffensiveRotation(),

                // Gap closers and utility
                CreateUtilityActions()
            );
        }

        private static Composite CreateEmergencyActions()
        {
            return new PrioritySelector(
                // Arcane Circle for damage reduction and healing
                new Decorator(ret => Me.CurrentHealthPercent < 40 && CanUseAbility(ArcaneCircle) &&
                                   EnemiesNearby() >= 2,
                    new Action(ret => UseAbility(ArcaneCircle, Me))),

                // Hell's Egress for emergency escape
                new Decorator(ret => Me.CurrentHealthPercent < 25 && CanUseAbility(HellsEgress) &&
                                   EnemiesNearby() >= 3,
                    new Action(ret => UseAbility(HellsEgress, Me))),

                // Regress to return to previous position if in danger
                new Decorator(ret => Me.CurrentHealthPercent < 30 && CanUseAbility(Regress) &&
                                   Me.HasAura("Threshold") && EnemiesNearby() >= 2,
                    new Action(ret => UseAbility(Regress, Me)))
            );
        }

        private static Composite CreateShroudActions()
        {
            return new PrioritySelector(
                // Perfectio - Ultimate ability (PvP Limit Break equivalent)
                new Decorator(ret => CanUseAbility(Perfectio) && HasValidTarget() &&
                                   EnemiesNearby(8f) >= 2,
                    new Action(ret => UseAbility(Perfectio, Target))),

                // Communio - End Enshroud with powerful finisher
                new Decorator(ret => CanUseAbility(Communio) && Me.HasAura("Enshrouded") &&
                                   GetLemureShroudStacks() <= 1,
                    new Action(ret => UseAbility(Communio, Target))),

                // Lemure abilities during Enshroud
                new Decorator(ret => Me.HasAura("Enshrouded") && GetLemureShroudStacks() > 0,
                    CreateLemureRotation()),

                // Void/Cross Reaping during Enshroud
                new Decorator(ret => Me.HasAura("Enshrouded"),
                    CreateEnshroudRotation()),

                // Enshroud activation
                new Decorator(ret => CanUseAbility(Enshroud) && GetSoulGauge() >= 50 &&
                                   HasValidTarget() && Target.CurrentHealthPercent > 30,
                    new Action(ret => UseAbility(Enshroud, Me))),

                // Tenebrae Lemurum - PvP specific shroud ability
                new Decorator(ret => CanUseAbility(TenebraeLemurum) && Me.HasAura("Enshrouded"),
                    new Action(ret => UseAbility(TenebraeLemurum, Target)))
            );
        }

        private static Composite CreateLemureRotation()
        {
            return new PrioritySelector(
                // Lemure Scythe for AoE
                new Decorator(ret => CanUseAbility(LemuresScythe) && EnemiesNearby(5f) >= 3,
                    new Action(ret => UseAbility(LemuresScythe, Target))),

                // Lemure Slice for single target
                new Decorator(ret => CanUseAbility(LemuresSlice),
                    new Action(ret => UseAbility(LemuresSlice, Target)))
            );
        }

        private static Composite CreateEnshroudRotation()
        {
            return new PrioritySelector(
                // Grim Reaping for AoE
                new Decorator(ret => CanUseAbility(GrimReaping) && EnemiesNearby(5f) >= 3,
                    new Action(ret => UseAbility(GrimReaping, Target))),

                // Cross Reaping (enhanced positional)
                new Decorator(ret => CanUseAbility(CrossReaping) && IsAtFlankOrRear(),
                    new Action(ret => UseAbility(CrossReaping, Target))),

                // Void Reaping (standard)
                new Decorator(ret => CanUseAbility(VoidReaping),
                    new Action(ret => UseAbility(VoidReaping, Target)))
            );
        }

        private static Composite CreateSoulGaugeActions()
        {
            return new PrioritySelector(
                // Gluttony for Soul Gauge generation and damage
                new Decorator(ret => CanUseAbility(Gluttony) && GetSoulGauge() < 50 &&
                                   EnemiesNearby(5f) >= 2,
                    new Action(ret => UseAbility(Gluttony, Target))),

                // Grim Swathe for AoE Soul spending
                new Decorator(ret => CanUseAbility(GrimSwathe) && GetSoulGauge() >= 50 &&
                                   EnemiesNearby(5f) >= 3,
                    new Action(ret => UseAbility(GrimSwathe, Target))),

                // Blood Stalk for single target Soul spending
                new Decorator(ret => CanUseAbility(BloodStalk) && GetSoulGauge() >= 50,
                    new Action(ret => UseAbility(BloodStalk, Target))),

                // Soul Scythe for AoE Soul generation
                new Decorator(ret => CanUseAbility(SoulScythe) && GetSoulGauge() < 50 &&
                                   EnemiesNearby(5f) >= 3,
                    new Action(ret => UseAbility(SoulScythe, Target))),

                // Soul Slice for single target Soul generation
                new Decorator(ret => CanUseAbility(SoulSlice) && GetSoulGauge() < 50,
                    new Action(ret => UseAbility(SoulSlice, Target)))
            );
        }

        private static Composite CreateOffensiveRotation()
        {
            return new PrioritySelector(
                // Death Warrant - PvP specific execute
                new Decorator(ret => CanUseAbility(DeathWarrant) && HasValidTarget() &&
                                   Target.CurrentHealthPercent < 50,
                    new Action(ret => UseAbility(DeathWarrant, Target))),

                // Plentiful Harvest - AoE finisher
                new Decorator(ret => CanUseAbility(PlentifulHarvest) && Me.HasAura("Immortal Sacrifice") &&
                                   EnemiesNearby(8f) >= 2,
                    new Action(ret => UseAbility(PlentifulHarvest, Target))),

                // Harvest Moon - Ranged attack
                new Decorator(ret => CanUseAbility(HarvestMoon) && HasValidTarget() &&
                                   Target.Distance() > 10f && Target.Distance() < 25f,
                    new Action(ret => UseAbility(HarvestMoon, Target))),

                // AoE rotation when multiple enemies
                new Decorator(ret => PvPSettings.Instance.UseAoE && EnemiesNearby(5f) >= 3,
                    CreateAoERotation()),

                // Single target rotation
                CreateSingleTargetRotation()
            );
        }

        private static Composite CreateAoERotation()
        {
            return new PrioritySelector(
                // Guillotine combo finisher
                new Decorator(ret => CanUseAbility(Guillotine) && Me.HasAura("Soul Reaver"),
                    new Action(ret => UseAbility(Guillotine, Target))),

                // Shadow of Death for DoT spread
                new Decorator(ret => CanUseAbility(ShadowOfDeath) &&
                                   !Target.HasMyAura("Death's Design"),
                    new Action(ret => UseAbility(ShadowOfDeath, Target))),

                // Infernal Slice combo
                new Decorator(ret => CanUseAbility(InfernalSlice) && Me.HasAura("Enhanced Waxing Slice"),
                    new Action(ret => UseAbility(InfernalSlice, Target))),

                // Waxing Slice combo
                new Decorator(ret => CanUseAbility(WaxingSlice) && Me.HasAura("Enhanced Slice"),
                    new Action(ret => UseAbility(WaxingSlice, Target))),

                // Slice starter
                new Decorator(ret => CanUseAbility(Slice),
                    new Action(ret => UseAbility(Slice, Target)))
            );
        }

        private static Composite CreateSingleTargetRotation()
        {
            return new PrioritySelector(
                // Gallows/Gibbet with Soul Reaver
                new Decorator(ret => Me.HasAura("Soul Reaver"),
                    CreateSoulReaverRotation()),

                // Shadow of Death for DoT
                new Decorator(ret => CanUseAbility(ShadowOfDeath) &&
                                   (!Target.HasMyAura("Death's Design") ||
                                    Target.GetAuraTimeLeft("Death's Design").TotalSeconds < 10),
                    new Action(ret => UseAbility(ShadowOfDeath, Target))),

                // Infernal Slice combo finisher
                new Decorator(ret => CanUseAbility(InfernalSlice) && Me.HasAura("Enhanced Waxing Slice"),
                    new Action(ret => UseAbility(InfernalSlice, Target))),

                // Waxing Slice combo
                new Decorator(ret => CanUseAbility(WaxingSlice) && Me.HasAura("Enhanced Slice"),
                    new Action(ret => UseAbility(WaxingSlice, Target))),

                // Slice starter
                new Decorator(ret => CanUseAbility(Slice),
                    new Action(ret => UseAbility(Slice, Target)))
            );
        }

        private static Composite CreateSoulReaverRotation()
        {
            return new PrioritySelector(
                // Gallows from rear
                new Decorator(ret => CanUseAbility(Gallows) && IsAtRear(),
                    new Action(ret => UseAbility(Gallows, Target))),

                // Gibbet from flank
                new Decorator(ret => CanUseAbility(Gibbet) && IsAtFlank(),
                    new Action(ret => UseAbility(Gibbet, Target))),

                // Default to Gibbet if positioning isn't optimal
                new Decorator(ret => CanUseAbility(Gibbet),
                    new Action(ret => UseAbility(Gibbet, Target))),

                // Fallback to Gallows
                new Decorator(ret => CanUseAbility(Gallows),
                    new Action(ret => UseAbility(Gallows, Target)))
            );
        }

        private static Composite CreateUtilityActions()
        {
            return new PrioritySelector(
                // Hell's Ingress for gap closing
                new Decorator(ret => CanUseAbility(HellsIngress) && HasValidTarget() &&
                                   Target.Distance() > 10f && Target.Distance() < 20f &&
                                   Me.CurrentHealthPercent > 50,
                    new Action(ret => UseAbility(HellsIngress, Target)))
            );
        }

        #region Helper Methods

        private static bool HasValidTarget()
        {
            return Target != null && Target.IsValid && Target.IsAlive &&
                   Target.CanAttack && Target.Distance() <= 30f;
        }

        private static bool CanUseAbility(uint abilityId)
        {
            return ActionManager.HasSpell(abilityId) &&
                   ActionManager.CanCast(abilityId, Target);
        }

        private static bool UseAbility(uint abilityId, GameObject target)
        {
            if (PvPSettings.Instance.DebugLogging)
            {
                PvPSettings.Instance.LogDebug($"Reaper using ability {abilityId} on {target?.Name ?? "self"}");
            }

            return ActionManager.DoAction(abilityId, target);
        }

        private static int EnemiesNearby(float range = 10f)
        {
            return GameObjectManager.GetObjectsOfType<BattleCharacter>()
                .Count(x => x.IsValid && x.IsAlive && x.CanAttack &&
                           x.Distance() <= range);
        }

        private static int GetSoulGauge()
        {
            // This would need to be implemented based on the actual gauge system
            // For now, return a placeholder value
            // In actual implementation, you'd read from the job gauge
            return 0;
        }

        private static int GetLemureShroudStacks()
        {
            // This would need to be implemented based on the actual shroud system
            // For now, return a placeholder value
            return Me.HasAura("Enshrouded") ? 5 : 0;
        }

        private static bool IsAtRear()
        {
            if (!HasValidTarget()) return false;

            // Calculate if player is behind the target
            var targetRotation = Target.Heading;
            var angleToPlayer = Math.Atan2(Me.Y - Target.Y, Me.X - Target.X);
            var relativeAngle = Math.Abs(targetRotation - angleToPlayer);

            // Normalize angle
            if (relativeAngle > Math.PI) relativeAngle = 2 * Math.PI - relativeAngle;

            // Rear is roughly 45 degrees behind (π/4 radians)
            return relativeAngle < Math.PI / 4;
        }

        private static bool IsAtFlank()
        {
            if (!HasValidTarget()) return false;

            // Calculate if player is at the side of the target
            var targetRotation = Target.Heading;
            var angleToPlayer = Math.Atan2(Me.Y - Target.Y, Me.X - Target.X);
            var relativeAngle = Math.Abs(targetRotation - angleToPlayer);

            // Normalize angle
            if (relativeAngle > Math.PI) relativeAngle = 2 * Math.PI - relativeAngle;

            // Flank is roughly 45-135 degrees to the side
            return relativeAngle >= Math.PI / 4 && relativeAngle <= 3 * Math.PI / 4;
        }

        private static bool IsAtFlankOrRear()
        {
            return IsAtFlank() || IsAtRear();
        }

        #endregion
    }
}
