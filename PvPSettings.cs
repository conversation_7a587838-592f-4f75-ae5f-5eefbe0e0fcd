using System;
using System.ComponentModel;
using System.Configuration;
using System.IO;
using System.Windows.Forms;
using ff14bot.Helpers;

namespace PvPRotation
{
    public class PvPSettings : JsonSettings
    {
        private static PvPSettings _instance;
        public static PvPSettings Instance => _instance ?? (_instance = new PvPSettings());

        private PvPSettings() : base(Path.Combine(CharacterSettingsDirectory, "PvPRotation.json"))
        {
        }

        private bool _isOpen;
        public bool IsOpen
        {
            get => _isOpen;
            set
            {
                _isOpen = value;
                if (value)
                {
                    ShowSettingsForm();
                }
            }
        }

        #region Emergency Settings

        [Setting]
        [DefaultValue(30)]
        [Description("Health percentage threshold to use Purify")]
        public int PurifyHealthThreshold { get; set; }

        [Setting]
        [DefaultValue(50)]
        [Description("Health percentage threshold to use Recuperate")]
        public int RecuperateHealthThreshold { get; set; }

        [Setting]
        [DefaultValue(25)]
        [Description("Health percentage threshold to use Guard")]
        public int GuardHealthThreshold { get; set; }

        #endregion

        #region Combat Settings

        [Setting]
        [DefaultValue(true)]
        [Description("Automatically use Sprint when needed")]
        public bool AutoSprint { get; set; }

        [Setting]
        [DefaultValue(true)]
        [Description("Use Limit Break when conditions are met")]
        public bool UseLimitBreak { get; set; }

        [Setting]
        [DefaultValue(3)]
        [Description("Minimum number of enemies nearby to use Limit Break")]
        public int LimitBreakEnemyCount { get; set; }

        [Setting]
        [DefaultValue(true)]
        [Description("Automatically target enemies")]
        public bool AutoTarget { get; set; }

        [Setting]
        [DefaultValue(true)]
        [Description("Prioritize low health enemies")]
        public bool PrioritizeLowHealth { get; set; }

        #endregion

        #region Movement Settings

        [Setting]
        [DefaultValue(true)]
        [Description("Automatically move to optimal combat position")]
        public bool AutoMovement { get; set; }

        [Setting]
        [DefaultValue(20.0f)]
        [Description("Maximum engagement range")]
        public float MaxEngagementRange { get; set; }

        [Setting]
        [DefaultValue(5.0f)]
        [Description("Minimum safe distance from enemies")]
        public float MinSafeDistance { get; set; }

        [Setting]
        [DefaultValue(true)]
        [Description("Automatically retreat when low on health")]
        public bool AutoRetreat { get; set; }

        [Setting]
        [DefaultValue(20)]
        [Description("Health percentage threshold to retreat")]
        public int RetreatHealthThreshold { get; set; }

        #endregion

        #region Job Specific Settings

        [Setting]
        [DefaultValue(true)]
        [Description("Use job-specific rotation logic")]
        public bool UseJobRotation { get; set; }

        [Setting]
        [DefaultValue(true)]
        [Description("Use AoE abilities when multiple enemies are present")]
        public bool UseAoE { get; set; }

        [Setting]
        [DefaultValue(3)]
        [Description("Minimum number of enemies for AoE abilities")]
        public int AoEEnemyCount { get; set; }

        [Setting]
        [DefaultValue(true)]
        [Description("Use crowd control abilities")]
        public bool UseCrowdControl { get; set; }

        [Setting]
        [DefaultValue(true)]
        [Description("Use defensive cooldowns")]
        public bool UseDefensiveCooldowns { get; set; }

        #endregion

        #region Advanced Settings

        [Setting]
        [DefaultValue(true)]
        [Description("Enable debug logging")]
        public bool DebugLogging { get; set; }

        [Setting]
        [DefaultValue(100)]
        [Description("Reaction time in milliseconds")]
        public int ReactionTime { get; set; }

        [Setting]
        [DefaultValue(true)]
        [Description("Respect global cooldown")]
        public bool RespectGCD { get; set; }

        [Setting]
        [DefaultValue(true)]
        [Description("Queue abilities during GCD")]
        public bool QueueAbilities { get; set; }

        #endregion

        private void ShowSettingsForm()
        {
            try
            {
                var form = new Form
                {
                    Text = "PvP Rotation Settings",
                    Size = new System.Drawing.Size(500, 600),
                    StartPosition = FormStartPosition.CenterScreen,
                    FormBorderStyle = FormBorderStyle.FixedDialog,
                    MaximizeBox = false,
                    MinimizeBox = false
                };

                var propertyGrid = new PropertyGrid
                {
                    Dock = DockStyle.Fill,
                    SelectedObject = this,
                    PropertySort = PropertySort.Categorized
                };

                var buttonPanel = new Panel
                {
                    Height = 40,
                    Dock = DockStyle.Bottom
                };

                var saveButton = new Button
                {
                    Text = "Save",
                    DialogResult = DialogResult.OK,
                    Anchor = AnchorStyles.Bottom | AnchorStyles.Right,
                    Location = new System.Drawing.Point(330, 8),
                    Size = new System.Drawing.Size(75, 23)
                };

                var cancelButton = new Button
                {
                    Text = "Cancel",
                    DialogResult = DialogResult.Cancel,
                    Anchor = AnchorStyles.Bottom | AnchorStyles.Right,
                    Location = new System.Drawing.Point(410, 8),
                    Size = new System.Drawing.Size(75, 23)
                };

                saveButton.Click += (sender, e) =>
                {
                    Save();
                    form.Close();
                    _isOpen = false;
                };

                cancelButton.Click += (sender, e) =>
                {
                    form.Close();
                    _isOpen = false;
                };

                buttonPanel.Controls.Add(saveButton);
                buttonPanel.Controls.Add(cancelButton);
                form.Controls.Add(propertyGrid);
                form.Controls.Add(buttonPanel);

                form.FormClosed += (sender, e) => _isOpen = false;

                form.Show();
            }
            catch (Exception ex)
            {
                Logging.WriteException(ex);
                _isOpen = false;
            }
        }

        public void LogDebug(string message)
        {
            if (DebugLogging)
            {
                Logging.Write($"[PvP Debug] {message}");
            }
        }
    }
}
