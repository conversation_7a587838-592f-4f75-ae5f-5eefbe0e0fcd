# PvP Rotation for Reborn Buddy

A customizable PvP rotation system for Final Fantasy XIV using Reborn Buddy.

## Features

- **Job-specific rotations**: Currently supports Warrior with framework for other jobs
- **Configurable settings**: Extensive settings panel for customization
- **Emergency actions**: Automatic use of defensive abilities when low on health
- **Smart targeting**: Automatic target selection and prioritization
- **Movement assistance**: Automatic positioning and retreat logic
- **PvP-specific abilities**: Integration with <PERSON>urify, Recuperate, Guard, and Sprint

## Installation

1. **Compile the project**:
   - Open the solution in Visual Studio
   - Set the RebornBuddyPath environment variable or update the project references
   - Build the project in Release mode

2. **Install the routine**:
   - Copy the compiled DLL to your `RebornBuddy\Routines` folder
   - Restart Reborn Buddy
   - Select "PvP Rotation" from the Combat Routine dropdown

## Configuration

Click the "Settings" button in Reborn Buddy to open the configuration panel. Key settings include:

### Emergency Settings
- **Purify Health Threshold**: Health % to automatically use Purify (default: 30%)
- **Recuperate Health Threshold**: Health % to use Recuperate (default: 50%)
- **Guard Health Threshold**: Health % to use Guard (default: 25%)

### Combat Settings
- **Auto Sprint**: Automatically use Sprint when needed
- **Use Limit Break**: Enable automatic Limit Break usage
- **Auto Target**: Automatically select and target enemies
- **Prioritize Low Health**: Target low-health enemies first

### Movement Settings
- **Auto Movement**: Automatically move to optimal positions
- **Max Engagement Range**: Maximum distance to engage enemies (default: 20.0)
- **Auto Retreat**: Automatically retreat when low on health
- **Retreat Health Threshold**: Health % to trigger retreat (default: 20%)

## Supported Jobs

### Warrior (Fully Implemented)
- Complete PvP rotation with combo management
- Beast Gauge utilization
- Emergency defensive abilities
- Gap closers and utility skills

### Reaper (Fully Implemented)
- Complete PvP rotation with Soul Gauge management
- Enshroud burst windows with Lemure abilities
- Positional awareness for Gibbet/Gallows optimization
- Death Warrant execute ability
- Hell's Ingress/Egress mobility tools
- Tenebrae Lemurum and Perfectio PvP abilities

### Other Jobs
The framework is in place to easily add other jobs. To add a new job:

1. Create a new file in the `Jobs` folder (e.g., `PaladinPvP.cs`)
2. Implement the job-specific rotation logic
3. Add the job case to the `CreateJobSpecificRotation()` method in `PvPRotation.cs`

## Customization

### Adding New Abilities
To add new abilities to a job rotation:

1. Add the ability ID constant at the top of the job file
2. Create the logic for when to use the ability
3. Add it to the appropriate priority selector in the rotation

### Modifying Behavior
The rotation uses TreeSharp behavior trees with priority selectors. Higher priority actions are checked first:

1. **Emergency actions** (healing, defensive abilities)
2. **Buff maintenance** (damage buffs, gauge management)
3. **Offensive rotation** (damage abilities, combos)
4. **Utility actions** (gap closers, ranged attacks)

## Troubleshooting

### Common Issues

1. **Rotation not working**:
   - Ensure you're in a PvP zone
   - Check that the routine is selected in Reborn Buddy
   - Verify ability IDs are correct for current patch

2. **Settings not saving**:
   - Check file permissions in the Reborn Buddy directory
   - Ensure the character settings folder exists

3. **Compilation errors**:
   - Verify all Reborn Buddy references are correct
   - Check that you're using .NET Framework 4.8
   - Update ability IDs if they've changed in recent patches

### Debug Mode
Enable "Debug Logging" in settings to see detailed information about ability usage and decision making.

## Contributing

To contribute to this project:

1. Fork the repository
2. Create a feature branch
3. Implement your changes
4. Test thoroughly in PvP environments
5. Submit a pull request

## Disclaimer

This rotation is for educational purposes. Use at your own risk and in accordance with the game's terms of service.

## Version History

- **v1.0.0**: Initial release with Warrior support and basic framework

## Credits

Based on the Reborn Buddy documentation and community examples.
